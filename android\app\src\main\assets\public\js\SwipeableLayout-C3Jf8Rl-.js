import{a as g,j as e}from"../assets/index-BaZyCK9t.js";import{r as n}from"./vendor-C67cHu0f.js";function k({message:d="Swipe to see more",onDismiss:i,position:f="top-right",autoHide:u=!0,autoHideDelay:a=5e3}){const{theme:s}=g(),[l,h]=n.useState(!0);n.useEffect(()=>{if(u){const o=setTimeout(()=>{h(!1),i&&i()},a);return()=>clearTimeout(o)}},[u,a,i]);const c=()=>{switch(f){case"top-left":return"top-4 left-4";case"top-right":return"top-4 right-4";case"bottom-left":return"bottom-4 left-4";case"bottom-right":return"bottom-4 right-4";case"center":return"top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2";default:return"top-4 right-4"}};return l?e.jsxs("div",{className:`absolute ${c()} z-30 pointer-events-none`,children:[e.jsxs("div",{className:`flex items-center gap-2 ${s.primary} text-white px-4 py-3 rounded-full shadow-lg backdrop-blur-sm animate-pulse swipe-indicator`,children:[e.jsx("div",{className:"flex items-center gap-1",children:e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-bounce"}),e.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})}),e.jsx("span",{className:"text-sm font-medium whitespace-nowrap",children:d}),e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"animate-pulse",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})})]}),i&&e.jsx("button",{onClick:()=>{h(!1),i()},className:"absolute -top-1 -right-1 w-5 h-5 bg-gray-600 text-white rounded-full flex items-center justify-center text-xs hover:bg-gray-700 transition-colors pointer-events-auto",children:"×"})]}):null}function I({leftContent:d,rightContent:i,className:f="",rightContentTitle:u="Additional Info"}){const{theme:a}=g(),[s,l]=n.useState("left"),[h,c]=n.useState(!0),[o,w]=n.useState(null),[x,p]=n.useState(null),[v,b]=n.useState(null),j=n.useRef(null),N=50;n.useEffect(()=>{const t=setTimeout(()=>{c(!1)},5e3);return()=>clearTimeout(t)},[]);const y=t=>{p(null),w(t.targetTouches[0].clientX),b(t.targetTouches[0].clientY)},S=t=>{const r=t.targetTouches[0];p(r.clientX);const m=Math.abs(r.clientX-o);Math.abs(r.clientY-(v||r.clientY))>m||m>10&&t.preventDefault()},T=()=>{if(!o||!x)return;const t=o-x,r=t>N,m=t<-50;r&&s==="left"?(l("right"),c(!1)):m&&s==="right"&&l("left")};return n.useEffect(()=>{const t=r=>{r.key==="Escape"&&s==="right"&&l("left")};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[s]),e.jsxs("div",{className:`${f}`,children:[e.jsxs("div",{className:"hidden lg:grid lg:grid-cols-2 gap-6 min-h-full",children:[e.jsx("div",{children:d}),e.jsx("div",{children:i})]}),e.jsxs("div",{className:"lg:hidden relative min-h-full",children:[h&&s==="left"&&e.jsx(k,{onDismiss:()=>c(!1),message:"Swipe left for more info"}),s==="right"&&e.jsx("div",{className:"absolute top-4 left-4 z-20",children:e.jsxs("button",{onClick:()=>l("left"),className:`flex items-center gap-2 ${a.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm`,children:[e.jsx("span",{className:"text-sm",children:"←"}),e.jsx("span",{className:"text-xs font-medium",children:"Back"})]})}),e.jsx("div",{ref:j,className:"relative w-full swipeable-container",onTouchStart:y,onTouchMove:S,onTouchEnd:T,style:{touchAction:"pan-y"},children:e.jsxs("div",{className:`flex w-[200%] swipe-transition swipeable-content ${s==="right"?"-translate-x-1/2":"translate-x-0"}`,children:[e.jsx("div",{className:"w-1/2 pr-2",children:e.jsx("div",{className:"pb-20",children:d})}),e.jsx("div",{className:"w-1/2 pl-2",children:e.jsxs("div",{className:"pb-20",children:[e.jsx("div",{className:`${a.card} rounded-2xl shadow-lg p-4 border ${a.border} mb-4`,children:e.jsx("h3",{className:`text-lg font-semibold ${a.text} text-center`,children:u})}),i]})})]})}),e.jsxs("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20",children:[e.jsx("div",{className:`w-2 h-2 rounded-full transition-colors ${s==="left"?a.primary:"bg-gray-400"}`}),e.jsx("div",{className:`w-2 h-2 rounded-full transition-colors ${s==="right"?a.primary:"bg-gray-400"}`})]})]})]})}export{I as S};
