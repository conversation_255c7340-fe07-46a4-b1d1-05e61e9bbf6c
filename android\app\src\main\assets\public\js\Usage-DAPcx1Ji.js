import{u as S,a as U,j as e,p as H}from"../assets/index-BaZyCK9t.js";import{B as E,C as A,b as D,L,c as P,d as B,p as G,a as I}from"./charts-B70j_Dbf.js";import{r as N}from"./vendor-C67cHu0f.js";import{H as m,l as y,m as p,p as w,q as v,d as W}from"./icons-Bhz3yUky.js";import{S as $}from"./ScrollHint-DfiBnkMs.js";import{S as z}from"./SwipeableLayout-C3Jf8Rl-.js";import{N as O}from"./NativeAd-CBEMc6TH.js";import"./utils-CgIdLkdF.js";function M(){const[a,h]=N.useState(""),[d,u]=N.useState(!1),{state:i,updateUsage:j,usageSinceLastRecording:f,getDisplayUnitName:s}=S(),{theme:t}=U(),l=parseFloat(a)||0,c=i.currentUnits-l,g=c*i.unitCost,b=async n=>{n.preventDefault(),u(!0);try{const o=parseFloat(a);if(isNaN(o)||o<0){alert("Please enter a valid meter reading (0 or greater)");return}if(o>i.currentUnits){alert("Current reading cannot be higher than your available units");return}j(o),h(""),alert(`Usage recorded successfully! Used ${c.toFixed(2)} ${s()} costing ${i.currencySymbol||"R"}${g.toFixed(2)}`)}catch(o){console.error("Error recording usage:",o),alert("Error recording usage. Please try again.")}finally{u(!1)}};return e.jsxs("form",{onSubmit:b,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"currentReading",className:`block text-sm font-semibold ${t.text} mb-3`,children:"Current Meter Reading"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:e.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${t.gradient}`,children:e.jsx(m,{className:"h-4 w-4 text-white"})})}),e.jsx("input",{type:"number",id:"currentReading",value:a,onChange:n=>h(n.target.value),onWheel:H,step:"0.01",min:"0",max:i.currentUnits,placeholder:"Enter current meter reading",className:`w-full pl-12 pr-4 py-4 md:py-4 border-4 ${t.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${t.border} ${t.card} ${t.text} ${t.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:!0})]}),e.jsx("p",{className:`mt-2 text-xs ${t.textSecondary} opacity-80 font-medium`,children:"📊 Enter the current reading from your electricity meter"})]}),l>0&&e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${t.card} border ${t.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${t.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${t.textSecondary} tracking-wider uppercase mb-1`,children:"READINGS"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${t.text} mb-1`,children:l.toFixed(2)}),e.jsxs("p",{className:`text-xs ${t.textSecondary}`,children:["Previous: ",i.currentUnits.toFixed(2)," ",s()]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${t.gradient} shadow-lg ml-3`,children:e.jsx(m,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${t.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${t.gradient} rounded-full opacity-15 blur-sm`})]}),e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${t.card} border ${t.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${t.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${t.textSecondary} tracking-wider uppercase mb-1`,children:"USAGE & COST"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${t.text} mb-1`,children:c.toFixed(2)}),e.jsxs("p",{className:`text-xs ${t.textSecondary}`,children:["Cost: ",i.currencySymbol||"R",g.toFixed(2)]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${t.gradient} shadow-lg ml-3`,children:e.jsx(m,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${t.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${t.gradient} rounded-full opacity-15 blur-sm`})]})]}),l>0&&c<0&&e.jsx("div",{className:`mt-4 p-4 ${t.secondary} border ${t.border} rounded-xl`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-1 rounded-lg ${t.accent} mr-2`,children:e.jsx("span",{className:"text-white text-xs",children:"⚠️"})}),e.jsx("span",{className:`${t.textSecondary} text-sm font-medium`,children:"Warning: New reading cannot be higher than available units"})]})}),e.jsx("button",{type:"submit",disabled:d||l<=0||l>i.currentUnits,className:`w-full ${t.primary} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:e.jsx("div",{className:"flex items-center justify-center gap-2 text-white",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),e.jsx("span",{className:"text-white",children:"Recording Usage..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"h-5 w-5 text-white"}),e.jsx("span",{className:"text-white",children:"Record Usage"})]})})}),e.jsxs("div",{className:`w-full text-center p-4 ${t.card} rounded-xl border ${t.border}`,children:[e.jsxs("div",{className:"w-full flex items-center justify-center mb-2",children:[e.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${t.gradient} mr-2`,children:e.jsx("span",{className:"text-white text-xs",children:"💡"})}),e.jsx("span",{className:`text-sm font-semibold ${t.text}`,children:"How it works"})]}),e.jsx("p",{className:`w-full text-xs ${t.textSecondary} leading-relaxed`,children:"Record your current meter reading to track electricity usage. The system will calculate how many units you've used since the last recording."})]}),e.jsxs("div",{className:`w-full p-6 ${t.card} rounded-xl border ${t.border} shadow-sm`,children:[e.jsxs("div",{className:"w-full flex items-center mb-4",children:[e.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${t.gradient} mr-3`,children:e.jsx(m,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${t.text} text-lg`,children:"Current Status"})]}),e.jsxs("div",{className:"w-full space-y-3 text-sm",children:[e.jsxs("div",{className:`w-full flex justify-between items-center p-3 ${t.secondary} rounded-lg`,children:[e.jsx("span",{className:`${t.textSecondary} font-medium`,children:"Available Units:"}),e.jsxs("span",{className:`${t.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",s()]})]}),e.jsxs("div",{className:`w-full flex justify-between items-center p-3 ${t.secondary} rounded-lg`,children:[e.jsx("span",{className:`${t.textSecondary} font-medium`,children:"Previous Reading:"}),e.jsxs("span",{className:`${t.text} font-bold`,children:[i.previousUnits.toFixed(2)," ",s()]})]}),e.jsxs("div",{className:`w-full flex justify-between items-center p-3 ${t.secondary} rounded-lg`,children:[e.jsx("span",{className:`${t.textSecondary} font-medium`,children:"Usage Since Last:"}),e.jsxs("span",{className:`${t.text} font-bold`,children:[f.toFixed(2)," ",s()]})]})]})]})]})}A.register(D,L,P,B,G,I);function Y(){const{state:a,usageSinceLastRecording:h,getDisplayUnitName:d,weeklyUsageTotal:u,monthlyUsageTotal:i,weeklyPurchaseTotal:j,monthlyPurchaseTotal:f}=S(),{theme:s,currentTheme:t}=U(),l=(r,x="bg-gray-800/50")=>t==="dark"?x:r,c=a.usageHistory.reduce((r,x)=>r+x.usage,0),g=a.usageHistory.length>0?c/a.usageHistory.length:0,b=r=>{const x=new Date(r),k=x.toLocaleDateString("en-GB"),T=x.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",hour12:!1});return{date:k,time:T}},n=a.usageHistory.slice(-7).reverse(),o={labels:n.length>0?n.map(r=>new Date(r.timestamp).toLocaleDateString("en-US",{month:"short",day:"numeric"})):["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:`Daily Usage (${d()})`,data:n.length>0?n.map(r=>r.usage):[12.5,15.2,8.7,22.1,18.9,14.3,16.8],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(251, 146, 60, 0.8)","rgba(14, 165, 233, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgba(99, 102, 241, 1)","rgba(139, 92, 246, 1)","rgba(236, 72, 153, 1)","rgba(34, 197, 94, 1)","rgba(251, 146, 60, 1)","rgba(14, 165, 233, 1)","rgba(168, 85, 247, 1)"],borderWidth:2,borderRadius:8,borderSkipped:!1}]},C={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Daily Usage Trend",font:{size:16,weight:"bold"},color:s.text==="text-gray-900"?"#1f2937":"#f9fafb",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(r){return`Usage: ${r.parsed.y.toFixed(2)} ${d()}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(156, 163, 175, 0.2)",drawBorder:!1},ticks:{color:s.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12},callback:function(r){return r+" "+d()}}},x:{grid:{display:!1},ticks:{color:s.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12}}}},animation:{duration:1500,easing:"easeInOutQuart"}},F=e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-4 md:p-6 border ${s.border} ${l("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")} w-full`,children:[e.jsxs("h2",{className:`text-xl font-semibold ${s.text} mb-4 flex items-center gap-3`,children:[e.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:e.jsx(m,{className:"h-5 w-5 text-white"})}),"Record New Reading"]}),e.jsx("div",{className:`${l("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4 w-full`,children:e.jsx(M,{})})]}),e.jsx(O,{className:"my-4"}),e.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 md:p-5 border ${s.border} min-h-24`,children:e.jsx($,{children:e.jsxs("div",{className:"flex items-center justify-between min-h-full min-w-max",children:[e.jsxs("div",{className:"flex items-center space-x-3 flex-shrink-0",children:[e.jsx("h3",{className:`text-base font-semibold ${s.text} whitespace-nowrap`,children:"This Week"}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:`p-1.5 rounded-lg ${s.secondary}`,children:e.jsx(y,{className:`h-4 w-4 ${s.textSecondary}`})}),e.jsx("div",{className:`p-1.5 rounded-lg ${s.secondary}`,children:e.jsx(p,{className:`h-4 w-4 ${s.textSecondary}`})})]})]}),e.jsxs("div",{className:"flex items-center space-x-6 flex-shrink-0",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:`text-sm font-medium ${s.textSecondary} whitespace-nowrap`,children:"Purchases"}),e.jsxs("p",{className:`text-lg font-bold ${s.text} whitespace-nowrap`,children:[a.currencySymbol,j.toFixed(2)]})]}),e.jsx("div",{className:`border-l ${s.border} h-10 w-px flex-shrink-0`}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:`text-sm font-medium ${s.textSecondary} whitespace-nowrap`,children:"Usage"}),e.jsxs("p",{className:`text-lg font-bold ${s.text} whitespace-nowrap`,children:[u.toFixed(2)," ",d()]}),e.jsxs("p",{className:`text-sm ${s.textSecondary} whitespace-nowrap`,children:["Cost: ",a.currencySymbol,(u*a.unitCost).toFixed(2)]})]})]})]})})}),e.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 md:p-5 border ${s.border} min-h-24`,children:e.jsx($,{children:e.jsxs("div",{className:"flex items-center justify-between min-h-full min-w-max",children:[e.jsxs("div",{className:"flex items-center space-x-3 flex-shrink-0",children:[e.jsx("h3",{className:`text-base font-semibold ${s.text} whitespace-nowrap`,children:"This Month"}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:`p-1.5 rounded-lg ${s.secondary}`,children:e.jsx(y,{className:`h-4 w-4 ${s.textSecondary}`})}),e.jsx("div",{className:`p-1.5 rounded-lg ${s.secondary}`,children:e.jsx(p,{className:`h-4 w-4 ${s.textSecondary}`})})]})]}),e.jsxs("div",{className:"flex items-center space-x-6 flex-shrink-0",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:`text-sm font-medium ${s.textSecondary} whitespace-nowrap`,children:"Purchases"}),e.jsxs("p",{className:`text-lg font-bold ${s.text} whitespace-nowrap`,children:[a.currencySymbol,f.toFixed(2)]})]}),e.jsx("div",{className:`border-l ${s.border} h-10 w-px flex-shrink-0`}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:`text-sm font-medium ${s.textSecondary} whitespace-nowrap`,children:"Usage"}),e.jsxs("p",{className:`text-lg font-bold ${s.text} whitespace-nowrap`,children:[i.toFixed(2)," ",d()]}),e.jsxs("p",{className:`text-sm ${s.textSecondary} whitespace-nowrap`,children:["Cost: ",a.currencySymbol,(i*a.unitCost).toFixed(2)]})]})]})]})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"CURRENT READING"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${s.text} mb-1`,children:a.currentUnits.toFixed(2)}),e.jsx("p",{className:`text-xs ${s.textSecondary}`,children:d()})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(m,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`})]}),a.usageHistory.length>0&&e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"USAGE SINCE LAST"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${s.text} mb-1`,children:h.toFixed(2)}),e.jsxs("p",{className:`text-xs ${s.textSecondary}`,children:[d()," used"]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(p,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`})]}),e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"TOTAL USAGE"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${s.text} mb-1`,children:c.toFixed(2)}),e.jsx("p",{className:`text-xs ${s.textSecondary}`,children:d()})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(w,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`})]}),e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${s.card} border ${s.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${s.textSecondary} tracking-wider uppercase mb-1`,children:"AVERAGE USAGE"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${s.text} mb-1`,children:g.toFixed(2)}),e.jsx("p",{className:`text-xs ${s.textSecondary}`,children:d()})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${s.gradient} shadow-lg ml-3`,children:e.jsx(v,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${s.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${s.gradient} rounded-full opacity-15 blur-sm`})]})]}),e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} ${l("bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50","bg-gray-800/50")}`,children:[e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{children:[e.jsxs("h2",{className:`text-xl font-bold ${s.text} flex items-center gap-3`,children:[e.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg",children:e.jsx(W,{className:"h-5 w-5 text-white"})}),"Usage Analytics"]}),e.jsx("p",{className:`mt-2 ${s.textSecondary} opacity-80 text-sm`,children:"Visual representation of your daily electricity consumption"})]})}),e.jsxs("div",{className:"h-64 relative",children:[e.jsx("div",{className:`absolute inset-0 ${l("bg-gradient-to-br from-white/80 to-white/40","bg-gray-700/40")} rounded-xl backdrop-blur-sm`}),e.jsx("div",{className:"relative h-full p-4",children:e.jsx(E,{data:o,options:C})})]})]}),e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} ${l("bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50","bg-gray-800/50")}`,children:[e.jsxs("h2",{className:`text-lg font-semibold ${s.text} mb-4 flex items-center gap-3`,children:[e.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md",children:e.jsx(v,{className:"h-4 w-4 text-white"})}),"How Usage is Calculated"]}),a.usageHistory.length>0&&e.jsx("div",{className:`p-4 ${s.card} rounded-xl border ${s.border} shadow-sm`,children:e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:`flex justify-between items-center p-3 ${s.secondary} rounded-lg border ${s.border}`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Previous Reading:"}),e.jsxs("span",{className:`${s.text} font-bold`,children:[a.previousUnits.toFixed(2)," ",d()]})]}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${s.secondary} rounded-lg border ${s.border}`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Current Reading:"}),e.jsxs("span",{className:`${s.text} font-bold`,children:[a.currentUnits.toFixed(2)," ",d()]})]}),e.jsx("div",{className:`border-t ${s.border} my-3`}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${s.secondary} rounded-lg border ${s.border}`,children:[e.jsx("span",{className:`${s.text} font-semibold`,children:"Usage Since Last Recording:"}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`${s.textSecondary} text-xs mb-1`,children:[a.previousUnits.toFixed(2)," - ",a.currentUnits.toFixed(2)]}),e.jsxs("span",{className:`${s.text} font-bold text-lg`,children:[h.toFixed(2)," ",d()]})]})]}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${s.secondary} rounded-lg border ${s.border}`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Cost of Usage:"}),e.jsxs("span",{className:`${s.text} font-bold`,children:[a.currencySymbol||"R",(h*a.unitCost).toFixed(2)]})]})]})})]})]}),R=e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} ${l("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")} flex flex-col`,children:[e.jsxs("h2",{className:`text-xl font-semibold ${s.text} mb-4 flex items-center gap-3`,children:[e.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:e.jsx(w,{className:"h-5 w-5 text-white"})}),"Recent Readings"]}),e.jsxs("div",{className:"space-y-2",children:[a.usageHistory.slice(0,10).map(r=>e.jsx("div",{className:`p-3 ${l("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl border ${l("border-white/40","border-gray-600")} shadow-sm hover:shadow-md transition-all duration-200`,children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:`font-semibold ${s.text} text-base`,children:[r.currentUnits.toFixed(2)," ",d()]}),e.jsxs("p",{className:`text-xs ${s.textSecondary} opacity-80 mt-1`,children:["Previous: ",r.previousUnits.toFixed(2)," ",d()]}),e.jsxs("div",{className:`text-xs ${s.textSecondary} mt-1 opacity-70`,children:[e.jsx("p",{children:b(r.timestamp).date}),e.jsx("p",{children:b(r.timestamp).time})]})]}),e.jsxs("div",{className:"text-right ml-3",children:[e.jsxs("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${r.usage>0?"bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200":"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200"}`,children:["-",r.usage.toFixed(2)," ",d()]}),e.jsxs("p",{className:`text-xs ${s.textSecondary} mt-1 font-medium`,children:[a.currencySymbol||"R",(r.usage*a.unitCost).toFixed(2)]})]})]})},r.id)),a.usageHistory.length===0&&e.jsxs("div",{className:`text-center py-12 ${l("bg-white/40","bg-gray-700/40")} backdrop-blur-sm rounded-xl border ${l("border-white/40","border-gray-600")}`,children:[e.jsx("div",{className:`p-4 rounded-2xl ${s.secondary} w-fit mx-auto mb-4`,children:e.jsx(p,{className:`h-12 w-12 ${s.textSecondary}`})}),e.jsx("p",{className:`text-sm ${s.textSecondary} opacity-80 font-medium`,children:"No usage records yet"}),e.jsx("p",{className:`text-xs ${s.textSecondary} opacity-60 mt-1`,children:"Record your first reading above to get started"})]})]})]});return e.jsx("div",{className:"space-y-6",children:e.jsx(z,{leftContent:F,rightContent:R,rightContentTitle:"Recent Readings",className:""})})}export{Y as default};
