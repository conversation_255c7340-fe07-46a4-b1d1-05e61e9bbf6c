import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { NavigationBar } from '@capgo/capacitor-navigation-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Force status bar to not overlay content - this should create proper spacing
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Show the status bar first to ensure it's visible
        await StatusBar.show();

        // Ensure navigation bar is visible and not hidden
        try {
          await NavigationBar.show();
        } catch (navShowError) {
          console.log('NavigationBar.show() not available:', navShowError);
        }

        // Define lighter theme colors for status bar and navigation bar (system bars)
        const systemBarColors = {
          'dark': '#1F2937',     // gray-800 (lighter than app header gray-900)
          'electric': '#93C5FD', // blue-300 (lighter than app header blue-550)
          'green': '#6EE7B7',    // green-300 (lighter than app header green-550)
          'teal': '#5EEAD4',     // teal-300 (lighter than app header teal-550)
          'pink': '#F9A8D4'      // pink-300 (lighter than app header pink-550)
        };

        // Get the lighter color for both status bar and navigation bar
        const systemBarColor = systemBarColors[currentTheme] || '#93C5FD';

        // Configure status bar - always use light content since all themes use dark backgrounds
        await StatusBar.setStyle({ style: 'LIGHT' });
        await StatusBar.setBackgroundColor({ color: systemBarColor });

        // Configure navigation bar to use the same lighter color as status bar
        console.log('Setting navigation bar color to:', systemBarColor);
        await NavigationBar.setColor({
          color: systemBarColor,
          darkButtons: false // Light buttons for all themes since we use dark backgrounds
        });

        // Also try setting navigation bar background color directly
        try {
          await NavigationBar.setNavigationBarColor({ color: systemBarColor });
        } catch (navError) {
          console.log('Alternative navigation bar method not available:', navError);
        }

      } catch (error) {
        console.log('StatusBar or NavigationBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
