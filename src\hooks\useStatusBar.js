import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { NavigationBar } from '@capgo/capacitor-navigation-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Force status bar to not overlay content - this should create proper spacing
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Show the status bar first to ensure it's visible
        await StatusBar.show();

        // Ensure navigation bar is visible and not hidden
        try {
          await NavigationBar.show();
        } catch (navShowError) {
          console.log('NavigationBar.show() not available:', navShowError);
        }

        // Define theme colors for status bar and navigation bar to match header (550 shades)
        const systemBarColors = {
          'dark': '#111827',     // gray-900 (same as app header)
          'electric': '#1a56db', // blue-550 (same as app header)
          'green': '#0e9f6e',    // green-550 (same as app header)
          'teal': '#0d9488',     // teal-550 (same as app header)
          'pink': '#db2777'      // pink-550 (same as app header)
        };

        // Get the theme color for both status bar and navigation bar (matching header)
        const systemBarColor = systemBarColors[currentTheme] || '#1a56db';

        // Configure status bar - always use light content since all themes use dark backgrounds
        await StatusBar.setStyle({ style: 'LIGHT' });
        await StatusBar.setBackgroundColor({ color: systemBarColor });

        // Configure navigation bar to use the same lighter color as status bar
        console.log('Setting navigation bar color to:', systemBarColor);
        await NavigationBar.setColor({
          color: systemBarColor,
          darkButtons: false // Light buttons for all themes since we use dark backgrounds
        });

        // Also try setting navigation bar background color directly
        try {
          await NavigationBar.setNavigationBarColor({ color: systemBarColor });
        } catch (navError) {
          console.log('Alternative navigation bar method not available:', navError);
        }

      } catch (error) {
        console.log('StatusBar or NavigationBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
