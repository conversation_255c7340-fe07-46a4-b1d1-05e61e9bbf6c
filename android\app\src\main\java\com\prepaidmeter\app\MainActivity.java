package com.prepaidmeter.app;

import android.os.Bundle;
import android.os.Build;
import android.view.Window;
import android.view.WindowManager;
import androidx.core.content.ContextCompat;
import com.getcapacitor.BridgeActivity;
import com.capacitorjs.plugins.localnotifications.LocalNotificationsPlugin;
import com.g12.capacitor.admob.ads.AdmobAdsPlugin;
import com.prepaidmeter.app.AdMobNativePlugin;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set navigation bar color programmatically
        setNavigationBarColor();

        // Register the LocalNotifications plugin
        registerPlugin(LocalNotificationsPlugin.class);

        // Register the AdmobAds plugin
        registerPlugin(AdmobAdsPlugin.class);

        // Register our custom AdMob Native plugin
        registerPlugin(AdMobNativePlugin.class);
    }

    private void setNavigationBarColor() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            // Set navigation bar color to light blue (#93C5FD)
            window.setNavigationBarColor(ContextCompat.getColor(this, R.color.navigationBarColor));

            // Set status bar color to match
            window.setStatusBarColor(ContextCompat.getColor(this, R.color.statusBarColor));

            // Set light navigation bar (dark icons) for API 26+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                int flags = window.getDecorView().getSystemUiVisibility();
                // Remove light navigation bar flag to keep icons light (since we have light blue background)
                flags &= ~android.view.View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR;
                window.getDecorView().setSystemUiVisibility(flags);
            }
        }
    }
}
